// src/logging/LoggerService.ts

import type {
    ILoggerService,
    ILogger,
    LogLevel,
    LoggerConfig,
    LoggerConfigurationUI,
    LoggerServiceStatus
} from './interfaces/ILogger';
import { LoggerFactory } from './LoggerFactory';
import { FileLogger } from './FileLogger';

/**
 * @class LoggerService
 * ILoggerService interface'ini uygulayan ana logger service sınıfı.
 * SOLID prensiplerinden Dependency Inversion Principle'a uygun olarak,
 * tüm loglama kararlarını bu seviyede alır.
 *
 * Alt bileşenler bu service'e bağımlı olur ve loglama kararları vermez.
 * Service, .env konfigürasyonuna göre hangi logger'ların kullan<PERSON>ğını,
 * log'ların nereye yazılacağını ve nasıl yönetileceğini belirler.
 */
export class LoggerService implements ILoggerService {
    private logger: ILogger;
    private config: LoggerConfig;
    private totalLogsWritten: number = 0;
    private lastActivity?: Date;
    private errors: string[] = [];
    private fileLogger?: FileLogger;

    constructor(config?: LoggerConfig) {
        this.config = config || LoggerFactory.createConfigFromEnv();
        this.logger = LoggerFactory.createLogger(this.config);

        // File logger'ı ayrıca sakla (dosya operasyonları için)
        if (this.config.toFile) {
            this.fileLogger = new FileLogger(this.config);
        }

        this.lastActivity = new Date();
    }

    // ILogger interface implementation
    debug(message: string, context?: Record<string, unknown>): void {
        this.logger.debug(message, context);
        this.updateActivity();
    }

    info(message: string, context?: Record<string, unknown>): void {
        this.logger.info(message, context);
        this.updateActivity();
    }

    warn(message: string, context?: Record<string, unknown>): void {
        this.logger.warn(message, context);
        this.updateActivity();
    }

    error(message: string, error?: Error, context?: Record<string, unknown>): void {
        this.logger.error(message, error, context);
        this.updateActivity();
    }

    fatal(message: string, error?: Error, context?: Record<string, unknown>): void {
        this.logger.fatal(message, error, context);
        this.updateActivity();
    }

    log(level: LogLevel, message: string, error?: Error, context?: Record<string, unknown>): void {
        this.logger.log(level, message, error, context);
        this.updateActivity();
    }

    getConfig(): LoggerConfig {
        return { ...this.config };
    }

    isLevelEnabled(level: LogLevel): boolean {
        return this.logger.isLevelEnabled(level);
    }

    // ILoggerService specific implementation
    async getFileLogs(): Promise<string[]> {
        try {
            if (!this.config.toFile || !this.fileLogger) {
                return [];
            }

            // Storage key'i hesapla (FileLogger'daki mantığın aynısı)
            const storageKey = `app_logs_${this.config.filePath?.replace(/[^a-zA-Z0-9]/g, '_') || 'default'}`;

            const storedLogs = localStorage.getItem(storageKey);
            if (storedLogs) {
                return JSON.parse(storedLogs);
            }

            return [];
        } catch (error) {
            const errorMessage = `Dosya log'ları alınırken hata: ${error}`;
            this.errors.push(errorMessage);
            console.error(errorMessage, error);
            return [];
        }
    }

    async clearFileLogs(): Promise<void> {
        try {
            if (!this.config.toFile || !this.fileLogger) {
                return;
            }

            this.fileLogger.clearLogs();
            this.info('Dosya log\'ları temizlendi', {
                timestamp: new Date().toISOString(),
                action: 'clearFileLogs'
            });
        } catch (error) {
            const errorMessage = `Dosya log'ları temizlenirken hata: ${error}`;
            this.errors.push(errorMessage);
            this.error('Dosya log\'ları temizleme hatası', error as Error);
            throw error;
        }
    }

    getConfigurationForUI(): LoggerConfigurationUI {
        const levelNames: Record<LogLevel, string> = {
            0: 'DEBUG',
            1: 'INFO',
            2: 'WARN',
            3: 'ERROR',
            4: 'FATAL'
        };

        const activeOutputs: string[] = [];
        if (this.config.toConsole) activeOutputs.push('Konsol');
        if (this.config.toFile) activeOutputs.push('Dosya');

        let status: 'full' | 'partial' | 'inactive';
        let statusMessage: string;
        let statusColor: 'positive' | 'warning' | 'negative';

        if (activeOutputs.length === 0) {
            status = 'inactive';
            statusMessage = 'Hiçbir loglama özelliği aktif değil';
            statusColor = 'negative';
        } else if (activeOutputs.length === 1) {
            status = 'partial';
            statusMessage = `Sadece ${activeOutputs[0].toLowerCase()} loglama aktif`;
            statusColor = 'warning';
        } else {
            status = 'full';
            statusMessage = 'Tüm loglama özellikleri aktif';
            statusColor = 'positive';
        }

        return {
            level: {
                name: levelNames[this.config.level],
                value: this.config.level
            },
            outputs: {
                console: {
                    enabled: this.config.toConsole,
                    status: this.config.toConsole ? 'active' : 'inactive'
                },
                file: {
                    enabled: this.config.toFile,
                    status: this.config.toFile ? 'active' : 'inactive',
                    ...(this.config.toFile && this.config.filePath && { path: this.config.filePath }),
                    ...(this.config.toFile && this.config.maxFileSize && { maxSize: this.formatFileSize(this.config.maxFileSize) })
                }
            },
            dateFormat: this.config.dateFormat || 'ISO',
            summary: {
                activeOutputs,
                totalOutputs: activeOutputs.length,
                status,
                statusMessage,
                statusColor
            }
        };
    }

    getServiceStatus(): LoggerServiceStatus {
        return {
            initialized: true,
            configurationSource: 'env',
            lastActivity: this.lastActivity,
            totalLogsWritten: this.totalLogsWritten,
            errors: [...this.errors]
        };
    }

    // Private helper methods
    private updateActivity(): void {
        this.lastActivity = new Date();
        this.totalLogsWritten++;
    }

    private formatFileSize(bytes?: number): string {
        if (!bytes) return 'Belirtilmemiş';

        const units = ['B', 'KB', 'MB', 'GB'];
        let size = bytes;
        let unitIndex = 0;

        while (size >= 1024 && unitIndex < units.length - 1) {
            size /= 1024;
            unitIndex++;
        }

        return `${size.toFixed(unitIndex === 0 ? 0 : 1)} ${units[unitIndex]}`;
    }
}
