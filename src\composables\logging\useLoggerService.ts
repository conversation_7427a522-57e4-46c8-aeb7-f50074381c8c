// src/composables/logging/useLoggerService.ts

import type { ILoggerService } from 'src/logging/interfaces/ILogger';
import { LoggerService } from 'src/logging/LoggerService';

// Logger service instance'ını bir kere oluşturup yeniden kullanmak için.
// Bu, uygulamanın yaşam döngüsü boyunca tek bir logger service örneği olmasını sağlar (Singleton).
let loggerServiceInstance: ILoggerService | null = null;

/**
 * @function useLoggerService
 * Uygulama genelinde logger service'ine erişim sağlayan Vue Composition API composable'ı.
 * SOLID prensiplerinden Dependency Inversion Principle'a uygun olarak,
 * alt bileşenler ILoggerService interface'ine bağımlı olur, somut implementasyona değil.
 * 
 * Bu composable, loglama ile ilgili tüm kararları service seviyesinde alır:
 * - <PERSON>i log seviyelerinin aktif olduğu
 * - Log'ların nereye yazıla<PERSON>ğı  
 * - Dosya log'larının nasıl yönetileceği
 * - Konfigürasyon bilgilerinin nasıl sunulacağı
 * 
 * Alt bileşenler sadece interface metodlarını çağırır, implementasyon detaylarını bilmez.
 * 
 * @returns ILoggerService Logger service interface'i
 * 
 * @example
 * ```typescript
 * // Vue bileşeninde kullanım
 * import { useLoggerService } from 'src/composables/logging/useLoggerService';
 * 
 * export default defineComponent({
 *   setup() {
 *     const loggerService = useLoggerService();
 *     
 *     const handleClick = () => {
 *       loggerService.info('Butona tıklandı', { userId: 123 });
 *     };
 *     
 *     const showLogs = async () => {
 *       const logs = await loggerService.getFileLogs();
 *       console.log('Dosya log\'ları:', logs);
 *     };
 *     
 *     const getConfig = () => {
 *       const uiConfig = loggerService.getConfigurationForUI();
 *       console.log('UI konfigürasyonu:', uiConfig);
 *     };
 *     
 *     return { handleClick, showLogs, getConfig };
 *   }
 * });
 * ```
 * 
 * @example
 * ```typescript
 * // Composable içinde kullanım
 * import { useLoggerService } from 'src/composables/logging/useLoggerService';
 * 
 * export function useApiService() {
 *   const loggerService = useLoggerService();
 *   
 *   const fetchData = async (url: string) => {
 *     try {
 *       loggerService.info('API çağrısı başlatıldı', { url });
 *       const response = await fetch(url);
 *       loggerService.info('API çağrısı başarılı', { url, status: response.status });
 *       return response.json();
 *     } catch (error) {
 *       loggerService.error('API çağrısı başarısız', error as Error, { url });
 *       throw error;
 *     }
 *   };
 *   
 *   return { fetchData };
 * }
 * ```
 */
export function useLoggerService(): ILoggerService {
    // Singleton pattern - logger service instance'ı sadece bir kere oluştur
    if (!loggerServiceInstance) {
        loggerServiceInstance = new LoggerService();
        
        // Logger service oluşturulduğunda bilgilendirme mesajı
        loggerServiceInstance.info('Logger service başlatıldı', {
            timestamp: new Date().toISOString(),
            serviceStatus: loggerServiceInstance.getServiceStatus(),
        });
    }
    
    return loggerServiceInstance;
}

/**
 * @function resetLoggerService
 * Logger service instance'ını sıfırlar.
 * Test amaçlı veya yapılandırma değişikliği sonrası kullanılır.
 * 
 * @example
 * ```typescript
 * // Test dosyasında
 * import { resetLoggerService, useLoggerService } from 'src/composables/logging/useLoggerService';
 * 
 * beforeEach(() => {
 *   resetLoggerService(); // Her test öncesi logger service'i sıfırla
 * });
 * ```
 */
export function resetLoggerService(): void {
    loggerServiceInstance = null;
}

/**
 * @function createCustomLoggerService
 * Özel yapılandırmalı logger service oluşturur.
 * Varsayılan service'den farklı ayarlara ihtiyaç duyulduğunda kullanılır.
 * 
 * @param config Logger yapılandırması
 * @returns Özel yapılandırmalı logger service
 * 
 * @example
 * ```typescript
 * import { createCustomLoggerService } from 'src/composables/logging/useLoggerService';
 * import { LogLevel } from 'src/logging/interfaces/ILogger';
 * 
 * // Sadece ERROR ve FATAL seviyelerini loglayan özel service
 * const errorLoggerService = createCustomLoggerService({
 *   level: LogLevel.ERROR,
 *   toConsole: true,
 *   toFile: true,
 *   filePath: 'logs/errors.log'
 * });
 * 
 * errorLoggerService.error('Kritik hata', error);
 * ```
 */
export function createCustomLoggerService(config: Parameters<typeof LoggerService>[0]): ILoggerService {
    return new LoggerService(config);
}

/**
 * @function isLogLevelEnabled
 * Belirtilen log seviyesinin aktif olup olmadığını kontrol eder.
 * SOLID prensiplerinden Dependency Inversion Principle'a uygun olarak,
 * konfigürasyon kararını LoggerService'e devreder.
 * 
 * @param level Kontrol edilecek log seviyesi
 * @returns Seviye aktif ise true, değilse false
 * 
 * @example
 * ```typescript
 * import { isLogLevelEnabled } from 'src/composables/logging/useLoggerService';
 * import { LogLevel } from 'src/logging/interfaces/ILogger';
 * 
 * if (isLogLevelEnabled(LogLevel.DEBUG)) {
 *   // DEBUG seviyesi aktif, pahalı debug işlemlerini yap
 *   const debugInfo = calculateExpensiveDebugInfo();
 *   loggerService.debug('Debug bilgisi', debugInfo);
 * }
 * ```
 */
export function isLogLevelEnabled(level: Parameters<ILoggerService['isLevelEnabled']>[0]): boolean {
    const loggerService = useLoggerService();
    return loggerService.isLevelEnabled(level);
}
