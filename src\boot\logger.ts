// src/boot/logger.ts

import { defineBoot } from '#q-app/wrappers';
import { useLoggerService } from 'src/composables/logging/useLoggerService';
import type { ILoggerService } from 'src/logging/interfaces/ILogger';

/**
 * Logger Boot Dosyası
 *
 * <PERSON>u dosya, Quasar uygulaması başlatılırken loglama sistemini yapılandırır ve başlatır.
 * SOLID prensiplerinden Single Responsibility Principle'a uygun olarak,
 * sadece logger başlatma işleminden sorumludur.
 *
 * Boot dosyası, uygulama başlangıcında çalışır ve:
 * 1. Logger sistemini .env yapılandırmasına göre başlatır
 * 2. Global hata yakalama mekanizmasını kurar
 * 3. Uygulama başlatma bilgilerini loglar
 * 4. Vue instance'ına logger'ı global olarak ekler (opsiyonel)
 */
export default defineBoot(({ app }) => {
    // Logger service'i başlat - SOLID prensiplerinden Dependency Inversion Principle'a uygun
    const loggerService = useLoggerService();

    // Uygulama başlatma bilgilerini logla
    loggerService.info('Uygulama başlatılıyor', {
        timestamp: new Date().toISOString(),
        userAgent: navigator.userAgent,
        url: window.location.href,
        environment: import.meta.env.MODE,
    });

    // Global hata yakalama mekanizması kur
    setupGlobalErrorHandling(loggerService);

    // Vue instance'ına logger service'i global property olarak ekle (opsiyonel)
    // Bu sayede Options API ile yazılmış bileşenlerde this.$logger şeklinde erişilebilir
    app.config.globalProperties.$logger = loggerService;

    // Development modunda ek bilgilendirme
    if (import.meta.env.DEV) {
        const configForUI = loggerService.getConfigurationForUI();
        loggerService.debug('Development modu aktif', {
            logConfig: configForUI,
            serviceStatus: loggerService.getServiceStatus(),
            envVars: {
                VITE_LOG_LEVEL: import.meta.env.VITE_LOG_LEVEL,
                VITE_LOG_TO_CONSOLE: import.meta.env.VITE_LOG_TO_CONSOLE,
                VITE_LOG_TO_FILE: import.meta.env.VITE_LOG_TO_FILE,
            },
        });
    }

    // Production modunda performans uyarısı
    if (import.meta.env.PROD && loggerService.isLevelEnabled(0)) { // DEBUG level
        const configForUI = loggerService.getConfigurationForUI();
        loggerService.warn('Production ortamında DEBUG seviyesi aktif', {
            recommendation: 'Performans için log seviyesini INFO veya üzerine çıkarın',
            currentLevel: configForUI.level.name,
        });
    }
});

/**
 * Global hata yakalama mekanizmasını kurar.
 * Yakalanmamış hatalar ve promise rejection'ları loglar.
 *
 * @param loggerService Logger service instance'ı
 */
function setupGlobalErrorHandling(loggerService: ILoggerService): void {
    // Yakalanmamış JavaScript hataları
    window.addEventListener('error', (event) => {
        loggerService.error('Yakalanmamış JavaScript hatası', event.error, {
            filename: event.filename,
            lineno: event.lineno,
            colno: event.colno,
            message: event.message,
            timestamp: new Date().toISOString(),
        });
    });

    // Yakalanmamış Promise rejection'ları
    window.addEventListener('unhandledrejection', (event) => {
        loggerService.error('Yakalanmamış Promise rejection', event.reason, {
            type: 'unhandledrejection',
            timestamp: new Date().toISOString(),
        });
    });

    // Vue hata yakalama (eğer Vue 3 error handler kullanılmıyorsa)
    const originalErrorHandler = window.console.error;
    window.console.error = (...args) => {
        // Vue hatalarını yakala
        if (args[0] && typeof args[0] === 'string' && args[0].includes('[Vue warn]')) {
            loggerService.warn('Vue uyarısı', {
                vueWarning: args.join(' '),
                timestamp: new Date().toISOString(),
            });
        }

        // Orijinal console.error'ı çağır
        originalErrorHandler.apply(console, args);
    };
}

/**
 * TypeScript için global property tanımı
 * Bu sayede Options API'de this.$logger kullanımı tip güvenli olur
 */
declare module '@vue/runtime-core' {
    interface ComponentCustomProperties {
        $logger: ILoggerService;
    }
}
