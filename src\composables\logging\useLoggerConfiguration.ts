// src/composables/logging/useLoggerConfiguration.ts

import { computed, readonly, ref } from 'vue';
import type { LoggerConfig, LogLevel } from 'src/logging/interfaces/ILogger';
import { useLoggerConfig } from 'src/logging/useLogger';

/**
 * @composable useLoggerConfiguration
 * Logger konfigürasyonu için Vue Composition API composable'ı.
 * SOLID prensiplerinden Single Responsibility Principle'a uygun olarak,
 * sadece logger konfigürasyon yönetimi ve UI helper'larından sorumludur.
 * 
 * Bu composable, alt bileşenlerin logger konfigürasyonuna erişmesini sağlar
 * ancak konfigürasyon kararlarını vermelerine izin vermez.
 * Konfigürasyon .env dosyasından LoggerConfigurationService tarafından yönetilir.
 */
export function useLoggerConfiguration() {
    // Logger konfigürasyonunu al
    const config = ref<LoggerConfig>(useLoggerConfig());

    /**
     * Log seviyesi numarasını okunabilir isime çevirir.
     * @param level Log seviyesi numarası
     * @returns Log seviyesi ismi
     */
    const getLogLevelName = (level: LogLevel): string => {
        const levelNames: Record<LogLevel, string> = {
            0: 'DEBUG',  // LogLevel.DEBUG
            1: 'INFO',   // LogLevel.INFO
            2: 'WARN',   // LogLevel.WARN
            3: 'ERROR',  // LogLevel.ERROR
            4: 'FATAL',  // LogLevel.FATAL
        };
        return levelNames[level] || 'UNKNOWN';
    };

    /**
     * Dosya boyutunu okunabilir formata çevirir.
     * @param bytes Byte cinsinden boyut
     * @returns Formatlanmış boyut string'i
     */
    const formatFileSize = (bytes?: number): string => {
        if (!bytes) return 'Belirtilmemiş';
        
        const units = ['B', 'KB', 'MB', 'GB'];
        let size = bytes;
        let unitIndex = 0;
        
        while (size >= 1024 && unitIndex < units.length - 1) {
            size /= 1024;
            unitIndex++;
        }
        
        return `${size.toFixed(unitIndex === 0 ? 0 : 1)} ${units[unitIndex]}`;
    };

    /**
     * Konfigürasyonun aktif özelliklerini sayar.
     * @returns Aktif özellik sayısı
     */
    const getActiveFeatureCount = computed(() => {
        let count = 0;
        if (config.value.toConsole) count++;
        if (config.value.toFile) count++;
        return count;
    });

    /**
     * Konfigürasyonun genel durumunu döndürür.
     * @returns Konfigürasyon durumu
     */
    const getConfigurationStatus = computed(() => {
        const activeFeatures = getActiveFeatureCount.value;
        
        if (activeFeatures === 0) {
            return {
                status: 'inactive',
                message: 'Hiçbir loglama özelliği aktif değil',
                color: 'negative'
            };
        } else if (activeFeatures === 1) {
            return {
                status: 'partial',
                message: config.value.toConsole ? 'Sadece konsol loglama aktif' : 'Sadece dosya loglama aktif',
                color: 'warning'
            };
        } else {
            return {
                status: 'full',
                message: 'Tüm loglama özellikleri aktif',
                color: 'positive'
            };
        }
    });

    /**
     * Konfigürasyonu yeniler.
     * Test amaçlı veya dinamik güncellemeler için kullanılır.
     */
    const refreshConfig = (): void => {
        config.value = useLoggerConfig();
    };

    /**
     * Konfigürasyonun özet bilgilerini döndürür.
     * @returns Konfigürasyon özeti
     */
    const getConfigurationSummary = computed(() => {
        return {
            level: getLogLevelName(config.value.level),
            outputs: [
                config.value.toConsole ? 'Konsol' : null,
                config.value.toFile ? 'Dosya' : null,
            ].filter(Boolean).join(', ') || 'Hiçbiri',
            filePath: config.value.toFile ? config.value.filePath : null,
            maxFileSize: config.value.toFile ? formatFileSize(config.value.maxFileSize) : null,
        };
    });

    return {
        // Reactive data
        config: readonly(config),
        
        // Computed properties
        activeFeatureCount: getActiveFeatureCount,
        configurationStatus: getConfigurationStatus,
        configurationSummary: getConfigurationSummary,
        
        // Helper functions
        getLogLevelName,
        formatFileSize,
        refreshConfig,
    };
}

/**
 * @type LoggerConfigurationComposable
 * useLoggerConfiguration composable'ının dönüş tipi.
 * TypeScript tip güvenliği için kullanılır.
 */
export type LoggerConfigurationComposable = ReturnType<typeof useLoggerConfiguration>;
