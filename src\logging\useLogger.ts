// src/logging/useLogger.ts

import type { ILogger, LoggerConfig } from './interfaces/ILogger';
import { LoggerFactory } from './LoggerFactory';

// Logger instance'ını bir kere oluşturup yeniden kullanmak için.
// Bu, uygulamanın yaşam döngüsü boyunca tek bir logger örneği olmasını sağlar (Singleton benzeri).
let loggerInstance: ILogger | null = null;

/**
 * @class LoggerConfigurationService
 * Logger konfigürasyonunu merkezi olarak yöneten service sınıfı.
 * SOLID prensiplerinden Single Responsibility Principle'a uygun olarak,
 * sadece logger konfigürasyon yönetiminden sorumludur.
 *
 * Bu service, .env dosyasından konfigürasyonu okur ve uygulama genelinde
 * tutarlı bir şekilde sunar. Alt bileşenler bu service'i kullanarak
 * konfigürasyona erişir, ancak konfigürasyon kararlarını vermez.
 */
class LoggerConfigurationService {
    private static instance: LoggerConfigurationService | null = null;
    private config: LoggerConfig | null = null;

    private constructor() {
        // Private constructor - Singleton pattern
    }

    /**
     * Singleton instance'ını döndürür.
     * @returns LoggerConfigurationService instance'ı
     */
    static getInstance(): LoggerConfigurationService {
        if (!LoggerConfigurationService.instance) {
            LoggerConfigurationService.instance = new LoggerConfigurationService();
        }
        return LoggerConfigurationService.instance;
    }

    /**
     * Logger konfigürasyonunu .env dosyasından yükler ve döndürür.
     * Konfigürasyon bir kere yüklendikten sonra cache'lenir.
     * @returns Logger konfigürasyonu
     */
    getConfig(): LoggerConfig {
        if (!this.config) {
            this.config = LoggerFactory.createConfigFromEnv();
        }
        return { ...this.config }; // Immutable copy döndür
    }

    /**
     * Konfigürasyonu sıfırlar. Test amaçlı kullanılır.
     */
    resetConfig(): void {
        this.config = null;
    }

    /**
     * Belirtilen log seviyesinin aktif olup olmadığını kontrol eder.
     * @param level Kontrol edilecek log seviyesi
     * @returns Seviye aktif ise true, değilse false
     */
    isLevelEnabled(level: number): boolean {
        const config = this.getConfig();
        return level >= config.level;
    }
}

/**
 * @function useLogger
 * Uygulama genelinde loglama servisine erişim sağlayan bir Vue Composition API composable'ı.
 * Bu composable, ILogger arayüzünü uygulayan somut bir logger örneğini döndürür.
 *
 * SOLID prensiplerinden Dependency Inversion Principle'a uygun olarak,
 * bileşenler doğrudan ConsoleLogger veya FileLogger'a bağımlı olmaz,
 * ILogger arayüzüne bağımlı olur. Bu, test edilebilirliği ve esnekliği artırır.
 *
 * @returns ILogger Loglama servisi arayüzü.
 *
 * @example
 * ```typescript
 * // Vue bileşeninde kullanım
 * import { useLogger } from 'src/logging/useLogger';
 *
 * export default defineComponent({
 *   setup() {
 *     const logger = useLogger();
 *
 *     const handleClick = () => {
 *       logger.info('Butona tıklandı', { userId: 123 });
 *     };
 *
 *     const handleError = (error: Error) => {
 *       logger.error('Bir hata oluştu', error, { component: 'MyComponent' });
 *     };
 *
 *     return { handleClick, handleError };
 *   }
 * });
 * ```
 *
 * @example
 * ```typescript
 * // Composable fonksiyonda kullanım
 * import { useLogger } from 'src/logging/useLogger';
 *
 * export function useApiService() {
 *   const logger = useLogger();
 *
 *   const fetchData = async () => {
 *     try {
 *       logger.debug('API çağrısı başlatılıyor');
 *       const response = await api.get('/data');
 *       logger.info('API çağrısı başarılı', { dataCount: response.data.length });
 *       return response.data;
 *     } catch (error) {
 *       logger.error('API çağrısı başarısız', error as Error);
 *       throw error;
 *     }
 *   };
 *
 *   return { fetchData };
 * }
 * ```
 */
export function useLogger(): ILogger {
    // Singleton pattern - logger instance'ı sadece bir kere oluştur
    if (!loggerInstance) {
        loggerInstance = LoggerFactory.createLogger();

        // Logger oluşturulduğunda bilgilendirme mesajı
        loggerInstance.info('Logger sistemi başlatıldı', {
            config: loggerInstance.getConfig(),
            timestamp: new Date().toISOString(),
        });
    }

    return loggerInstance;
}

/**
 * @function resetLogger
 * Logger instance'ını sıfırlar.
 * Test amaçlı veya yapılandırma değişikliği sonrası kullanılır.
 *
 * @example
 * ```typescript
 * // Test dosyasında
 * import { resetLogger, useLogger } from 'src/logging/useLogger';
 *
 * beforeEach(() => {
 *   resetLogger(); // Her test öncesi logger'ı sıfırla
 * });
 * ```
 */
export function resetLogger(): void {
    loggerInstance = null;
}

/**
 * @function createCustomLogger
 * Özel yapılandırmalı logger oluşturur.
 * Varsayılan logger'dan farklı ayarlara ihtiyaç duyulduğunda kullanılır.
 *
 * @param config Logger yapılandırması
 * @returns Özel yapılandırmalı logger
 *
 * @example
 * ```typescript
 * import { createCustomLogger } from 'src/logging/useLogger';
 * import { LogLevel } from 'src/logging/interfaces/ILogger';
 *
 * // Sadece ERROR ve FATAL seviyelerini loglayan özel logger
 * const errorLogger = createCustomLogger({
 *   level: LogLevel.ERROR,
 *   toConsole: true,
 *   toFile: true,
 *   filePath: 'logs/errors.log'
 * });
 *
 * errorLogger.error('Kritik hata', error);
 * ```
 */
export function createCustomLogger(config: Parameters<typeof LoggerFactory.createLogger>[0]): ILogger {
    return LoggerFactory.createLogger(config);
}

/**
 * @function useLoggerConfig
 * Logger konfigürasyonuna erişim sağlayan composable.
 * SOLID prensiplerinden Dependency Inversion Principle'a uygun olarak,
 * alt bileşenler konfigürasyon kararı vermez, sadece mevcut konfigürasyonu okur.
 *
 * @returns Logger konfigürasyonu
 *
 * @example
 * ```typescript
 * import { useLoggerConfig } from 'src/logging/useLogger';
 *
 * const config = useLoggerConfig();
 * console.log('Mevcut log seviyesi:', config.level);
 * ```
 */
export function useLoggerConfig(): LoggerConfig {
    const configService = LoggerConfigurationService.getInstance();
    return configService.getConfig();
}

/**
 * @function isLogLevelEnabled
 * Belirtilen log seviyesinin aktif olup olmadığını kontrol eder.
 * Performans optimizasyonu için kullanılır.
 *
 * @param level Kontrol edilecek log seviyesi
 * @returns Seviye aktif ise true, değilse false
 *
 * @example
 * ```typescript
 * import { useLogger, isLogLevelEnabled } from 'src/logging/useLogger';
 * import { LogLevel } from 'src/logging/interfaces/ILogger';
 *
 * const logger = useLogger();
 *
 * // Pahalı işlem sadece DEBUG aktifse yapılsın
 * if (isLogLevelEnabled(LogLevel.DEBUG)) {
 *   const expensiveDebugData = generateExpensiveDebugData();
 *   logger.debug('Debug verisi', expensiveDebugData);
 * }
 * ```
 */
export function isLogLevelEnabled(level: Parameters<ILogger['isLevelEnabled']>[0]): boolean {
    const configService = LoggerConfigurationService.getInstance();
    return configService.isLevelEnabled(level);
}
