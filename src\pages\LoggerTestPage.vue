<template>
  <q-page class="flex flex-col q-pa-md">
    <div class="text-h4 q-mb-md">Logger Test Sayfası</div>

    <!-- Logger Yapılandırması -->
    <q-card class="q-mb-md">
      <q-card-section>
        <div class="text-h6">Mevcut Logger Yapılandırması</div>
        <div class="text-body2 q-mt-sm">
          Logger yapılandırması .env dosyasından otomatik olarak yüklenir.
          <br />
          Alt bileşenler bu yapılandırmaya müdahale etmez (Dependency Inversion Principle).
        </div>

        <!-- Konfigürasyon Detayları -->
        <q-separator class="q-my-md" />
        <div class="row q-gutter-md">
          <div class="col-12 col-md-6">
            <q-list dense>
              <q-item>
                <q-item-section>
                  <q-item-label caption>Log Seviyesi</q-item-label>
                  <q-item-label>{{ getLogLevelName(loggerConfig.level) }}</q-item-label>
                </q-item-section>
              </q-item>
              <q-item>
                <q-item-section>
                  <q-item-label caption>Konsol Loglama</q-item-label>
                  <q-item-label>
                    <q-badge :color="loggerConfig.toConsole ? 'positive' : 'negative'">
                      {{ loggerConfig.toConsole ? 'Aktif' : 'Pasif' }}
                    </q-badge>
                  </q-item-label>
                </q-item-section>
              </q-item>
              <q-item>
                <q-item-section>
                  <q-item-label caption>Dosya Loglama</q-item-label>
                  <q-item-label>
                    <q-badge :color="loggerConfig.toFile ? 'positive' : 'negative'">
                      {{ loggerConfig.toFile ? 'Aktif' : 'Pasif' }}
                    </q-badge>
                  </q-item-label>
                </q-item-section>
              </q-item>
            </q-list>
          </div>
          <div class="col-12 col-md-6">
            <q-list dense>
              <q-item v-if="loggerConfig.toFile">
                <q-item-section>
                  <q-item-label caption>Dosya Yolu</q-item-label>
                  <q-item-label>{{ loggerConfig.filePath }}</q-item-label>
                </q-item-section>
              </q-item>
              <q-item v-if="loggerConfig.toFile">
                <q-item-section>
                  <q-item-label caption>Maksimum Dosya Boyutu</q-item-label>
                  <q-item-label>{{ formatFileSize(loggerConfig.maxFileSize) }}</q-item-label>
                </q-item-section>
              </q-item>
              <q-item>
                <q-item-section>
                  <q-item-label caption>Tarih Formatı</q-item-label>
                  <q-item-label>{{ loggerConfig.dateFormat }}</q-item-label>
                </q-item-section>
              </q-item>
            </q-list>
          </div>
        </div>
      </q-card-section>
    </q-card>

    <!-- Test Butonları -->
    <q-card class="q-mb-md">
      <q-card-section>
        <div class="text-h6 q-mb-md">Log Seviyesi Testleri</div>

        <div class="row q-gutter-sm q-mb-md">
          <q-btn
            color="grey"
            label="DEBUG Log"
            @click="testDebugLog"
            :disable="!logger.isLevelEnabled(LogLevel.DEBUG)"
          />
          <q-btn
            color="blue"
            label="INFO Log"
            @click="testInfoLog"
            :disable="!logger.isLevelEnabled(LogLevel.INFO)"
          />
          <q-btn
            color="orange"
            label="WARN Log"
            @click="testWarnLog"
            :disable="!logger.isLevelEnabled(LogLevel.WARN)"
          />
          <q-btn
            color="red"
            label="ERROR Log"
            @click="testErrorLog"
            :disable="!logger.isLevelEnabled(LogLevel.ERROR)"
          />
          <q-btn
            color="deep-purple"
            label="FATAL Log"
            @click="testFatalLog"
            :disable="!logger.isLevelEnabled(LogLevel.FATAL)"
          />
        </div>

        <div class="row q-gutter-sm">
          <q-btn color="primary" label="Context ile Log" @click="testContextLog" />
          <q-btn color="negative" label="Error Objesi ile Log" @click="testErrorObjectLog" />
          <q-btn color="secondary" label="Performans Testi" @click="testPerformance" />
        </div>
      </q-card-section>
    </q-card>

    <!-- File Logger Kontrolleri -->
    <q-card class="q-mb-md">
      <q-card-section>
        <div class="text-h6 q-mb-md">File Logger Kontrolleri</div>

        <div class="row q-gutter-sm q-mb-md">
          <q-btn color="info" label="Log'ları Göster" @click="showFileLogs" />
          <q-btn color="warning" label="Log'ları Temizle" @click="clearFileLogs" />
        </div>

        <!-- File Logs Display -->
        <q-expansion-item
          v-if="fileLogs.length > 0"
          icon="description"
          label="Dosya Log'ları"
          class="q-mt-md"
        >
          <q-card>
            <q-card-section>
              <div class="text-caption q-mb-sm">Toplam {{ fileLogs.length }} log kaydı</div>
              <pre class="file-logs">{{ fileLogs.join('\n') }}</pre>
            </q-card-section>
          </q-card>
        </q-expansion-item>
      </q-card-section>
    </q-card>

    <!-- Test Sonuçları -->
    <q-card>
      <q-card-section>
        <div class="text-h6 q-mb-md">Test Sonuçları</div>
        <div class="text-body2">
          Konsol çıktısını görmek için tarayıcı geliştirici araçlarını açın.
          <br />
          File logging aktifse, log'lar localStorage'da saklanır.
        </div>
      </q-card-section>
    </q-card>
  </q-page>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { useLogger } from 'src/logging/useLogger';
import { LogLevel } from 'src/logging/interfaces/ILogger';
import { FileLogger } from 'src/logging/FileLogger';
import { useLoggerConfiguration } from 'src/composables/logging/useLoggerConfiguration';

// Logger ve yapılandırma
const logger = useLogger();
const { config: loggerConfig, getLogLevelName, formatFileSize } = useLoggerConfiguration();
const fileLogs = ref<string[]>([]);

// Test sayacı
let testCounter = 0;

onMounted(() => {
  logger.info('Logger Test Sayfası yüklendi', {
    timestamp: new Date().toISOString(),
    page: 'LoggerTestPage',
  });
});

/**
 * DEBUG seviyesi log testi
 */
function testDebugLog(): void {
  testCounter++;
  logger.debug(`DEBUG test mesajı #${testCounter}`, {
    testType: 'debug',
    counter: testCounter,
    timestamp: new Date().toISOString(),
  });
}

/**
 * INFO seviyesi log testi
 */
function testInfoLog(): void {
  testCounter++;
  logger.info(`INFO test mesajı #${testCounter}`, {
    testType: 'info',
    counter: testCounter,
    userAction: 'button_click',
  });
}

/**
 * WARN seviyesi log testi
 */
function testWarnLog(): void {
  testCounter++;
  logger.warn(`WARN test mesajı #${testCounter}`, {
    testType: 'warning',
    counter: testCounter,
    warning: 'Bu bir test uyarısıdır',
  });
}

/**
 * ERROR seviyesi log testi
 */
function testErrorLog(): void {
  testCounter++;
  logger.error(`ERROR test mesajı #${testCounter}`, undefined, {
    testType: 'error',
    counter: testCounter,
    errorCode: 'TEST_ERROR',
  });
}

/**
 * FATAL seviyesi log testi
 */
function testFatalLog(): void {
  testCounter++;
  logger.fatal(`FATAL test mesajı #${testCounter}`, undefined, {
    testType: 'fatal',
    counter: testCounter,
    severity: 'critical',
  });
}

/**
 * Context ile log testi
 */
function testContextLog(): void {
  testCounter++;
  const complexContext = {
    user: {
      id: 123,
      name: 'Test User',
      roles: ['admin', 'user'],
    },
    request: {
      method: 'POST',
      url: '/api/test',
      headers: {
        'Content-Type': 'application/json',
      },
    },
    metadata: {
      testCounter,
      timestamp: new Date().toISOString(),
      browser: navigator.userAgent,
    },
  };

  logger.info('Karmaşık context ile test', complexContext);
}

/**
 * Error objesi ile log testi
 */
function testErrorObjectLog(): void {
  testCounter++;
  try {
    // Kasıtlı hata oluştur
    throw new Error(`Test hatası #${testCounter}`);
  } catch (error) {
    logger.error('Yakalanan test hatası', error as Error, {
      testType: 'error_object',
      counter: testCounter,
      errorSource: 'manual_test',
    });
  }
}

/**
 * Performans testi - çok sayıda log mesajı
 */
function testPerformance(): void {
  const startTime = performance.now();
  const logCount = 100;

  logger.info(`Performans testi başlatılıyor - ${logCount} log mesajı`);

  for (let i = 0; i < logCount; i++) {
    if (i % 4 === 0) {
      logger.debug(`Performans test DEBUG #${i}`);
    } else if (i % 4 === 1) {
      logger.info(`Performans test INFO #${i}`);
    } else if (i % 4 === 2) {
      logger.warn(`Performans test WARN #${i}`);
    } else {
      logger.error(`Performans test ERROR #${i}`);
    }
  }

  const endTime = performance.now();
  const duration = endTime - startTime;

  logger.info('Performans testi tamamlandı', {
    logCount,
    duration: `${duration.toFixed(2)}ms`,
    averagePerLog: `${(duration / logCount).toFixed(4)}ms`,
  });
}

/**
 * File log'ları göster (sadece FileLogger kullanılıyorsa)
 */
function showFileLogs(): void {
  try {
    // Mevcut config'i al
    const config = loggerConfig.value!;

    // Storage key'i hesapla (FileLogger constructor'ındaki mantığın aynısı)
    const storageKey = `app_logs_${config.filePath?.replace(/[^a-zA-Z0-9]/g, '_') || 'default'}`;

    console.log('Aranan storage key:', storageKey);
    console.log('Mevcut localStorage keys:', Object.keys(localStorage));

    // Doğrudan localStorage'dan al
    const storedLogs = localStorage.getItem(storageKey);
    if (storedLogs) {
      fileLogs.value = JSON.parse(storedLogs);
    } else {
      fileLogs.value = [];
    }

    logger.info("File log'ları gösterildi", {
      logCount: fileLogs.value.length,
      storageKey: storageKey,
    });
  } catch (error) {
    console.error("File log'ları gösterme hatası:", error);
    logger.error("File log'ları gösterme hatası", error as Error);
  }
}

/**
 * File log'ları temizle
 */
function clearFileLogs(): void {
  const fileLogger = new FileLogger(loggerConfig);
  fileLogger.clearLogs();
  fileLogs.value = [];

  logger.info("File log'ları temizlendi");
}
</script>

<style scoped>
.file-logs {
  max-height: 300px;
  overflow-y: auto;
  background-color: #f5f5f5;
  padding: 8px;
  border-radius: 4px;
  font-size: 12px;
  line-height: 1.4;
}

pre {
  white-space: pre-wrap;
  word-wrap: break-word;
}
</style>
