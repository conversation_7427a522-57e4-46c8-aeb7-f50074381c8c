<template>
  <q-page class="flex flex-col q-pa-md">
    <div class="text-h4 q-mb-md">Logger Test Sayfası</div>

    <!-- Logger Yapılandırması -->
    <q-card class="q-mb-md">
      <q-card-section>
        <div class="text-h6">Mevcut Logger Yapılandırması</div>
        <div class="text-body2 q-mt-sm">
          Logger yapılandırması .env dosyasından otomatik olarak yüklenir.
          <br />
          Alt bileşenler bu yapılandırmaya müdahale etmez (Dependency Inversion Principle).
        </div>

        <!-- Konfigürasyon Detayları -->
        <q-separator class="q-my-md" />
        <div class="row q-gutter-md">
          <div class="col-12 col-md-6">
            <q-list dense>
              <q-item>
                <q-item-section>
                  <q-item-label caption>Log Seviyesi</q-item-label>
                  <q-item-label>{{ loggerConfig.level.name }}</q-item-label>
                </q-item-section>
              </q-item>
              <q-item>
                <q-item-section>
                  <q-item-label caption>Konsol Loglama</q-item-label>
                  <q-item-label>
                    <q-badge
                      :color="loggerConfig.outputs.console.enabled ? 'positive' : 'negative'"
                    >
                      {{ loggerConfig.outputs.console.enabled ? 'Aktif' : 'Pasif' }}
                    </q-badge>
                  </q-item-label>
                </q-item-section>
              </q-item>
              <q-item>
                <q-item-section>
                  <q-item-label caption>Dosya Loglama</q-item-label>
                  <q-item-label>
                    <q-badge :color="loggerConfig.outputs.file.enabled ? 'positive' : 'negative'">
                      {{ loggerConfig.outputs.file.enabled ? 'Aktif' : 'Pasif' }}
                    </q-badge>
                  </q-item-label>
                </q-item-section>
              </q-item>
            </q-list>
          </div>
          <div class="col-12 col-md-6">
            <q-list dense>
              <q-item v-if="loggerConfig.outputs.file.enabled && loggerConfig.outputs.file.path">
                <q-item-section>
                  <q-item-label caption>Dosya Yolu</q-item-label>
                  <q-item-label>{{ loggerConfig.outputs.file.path }}</q-item-label>
                </q-item-section>
              </q-item>
              <q-item v-if="loggerConfig.outputs.file.enabled && loggerConfig.outputs.file.maxSize">
                <q-item-section>
                  <q-item-label caption>Maksimum Dosya Boyutu</q-item-label>
                  <q-item-label>{{ loggerConfig.outputs.file.maxSize }}</q-item-label>
                </q-item-section>
              </q-item>
              <q-item>
                <q-item-section>
                  <q-item-label caption>Tarih Formatı</q-item-label>
                  <q-item-label>{{ loggerConfig.dateFormat }}</q-item-label>
                </q-item-section>
              </q-item>
              <q-item>
                <q-item-section>
                  <q-item-label caption>Durum</q-item-label>
                  <q-item-label>
                    <q-badge :color="loggerConfig.summary.statusColor">
                      {{ loggerConfig.summary.statusMessage }}
                    </q-badge>
                  </q-item-label>
                </q-item-section>
              </q-item>
            </q-list>
          </div>
        </div>
      </q-card-section>
    </q-card>

    <!-- Test Butonları -->
    <q-card class="q-mb-md">
      <q-card-section>
        <div class="text-h6 q-mb-md">Log Seviyesi Testleri</div>

        <div class="row q-gutter-sm q-mb-md">
          <q-btn
            color="grey"
            label="DEBUG Log"
            @click="testDebugLog"
            :disable="!loggerService.isLevelEnabled(LogLevel.DEBUG)"
          />
          <q-btn
            color="blue"
            label="INFO Log"
            @click="testInfoLog"
            :disable="!loggerService.isLevelEnabled(LogLevel.INFO)"
          />
          <q-btn
            color="orange"
            label="WARN Log"
            @click="testWarnLog"
            :disable="!loggerService.isLevelEnabled(LogLevel.WARN)"
          />
          <q-btn
            color="red"
            label="ERROR Log"
            @click="testErrorLog"
            :disable="!loggerService.isLevelEnabled(LogLevel.ERROR)"
          />
          <q-btn
            color="deep-purple"
            label="FATAL Log"
            @click="testFatalLog"
            :disable="!loggerService.isLevelEnabled(LogLevel.FATAL)"
          />
        </div>

        <div class="row q-gutter-sm">
          <q-btn color="primary" label="Context ile Log" @click="testContextLog" />
          <q-btn color="negative" label="Error Objesi ile Log" @click="testErrorObjectLog" />
          <q-btn color="secondary" label="Performans Testi" @click="testPerformance" />
        </div>
      </q-card-section>
    </q-card>

    <!-- File Logger Kontrolleri -->
    <q-card class="q-mb-md">
      <q-card-section>
        <div class="text-h6 q-mb-md">File Logger Kontrolleri</div>

        <div class="row q-gutter-sm q-mb-md">
          <q-btn color="info" label="Log'ları Göster" @click="showFileLogs" />
          <q-btn color="warning" label="Log'ları Temizle" @click="clearFileLogs" />
        </div>

        <!-- File Logs Display -->
        <q-expansion-item
          v-if="fileLogs.length > 0"
          icon="description"
          label="Dosya Log'ları"
          class="q-mt-md"
        >
          <q-card>
            <q-card-section>
              <div class="text-caption q-mb-sm">Toplam {{ fileLogs.length }} log kaydı</div>
              <pre class="file-logs">{{ fileLogs.join('\n') }}</pre>
            </q-card-section>
          </q-card>
        </q-expansion-item>
      </q-card-section>
    </q-card>

    <!-- Test Sonuçları -->
    <q-card>
      <q-card-section>
        <div class="text-h6 q-mb-md">Test Sonuçları</div>
        <div class="text-body2">
          Konsol çıktısını görmek için tarayıcı geliştirici araçlarını açın.
          <br />
          File logging aktifse, log'lar localStorage'da saklanır.
        </div>
      </q-card-section>
    </q-card>
  </q-page>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue';
import { LogLevel } from 'src/logging/interfaces/ILogger';
import { useLoggerService } from 'src/composables/logging/useLoggerService';

// Logger service - SOLID prensiplerinden Dependency Inversion Principle'a uygun
// Alt bileşen (bu sayfa) loglama kararları vermez, sadece interface'i kullanır
const loggerService = useLoggerService();

// UI için konfigürasyon bilgisi - service'den alınır, alt bileşen karar vermez
const loggerConfig = computed(() => loggerService.getConfigurationForUI());
const fileLogs = ref<string[]>([]);

// Test sayacı
let testCounter = 0;

onMounted(() => {
  loggerService.info('Logger Test Sayfası yüklendi', {
    timestamp: new Date().toISOString(),
    page: 'LoggerTestPage',
  });
});

/**
 * DEBUG seviyesi log testi
 */
function testDebugLog(): void {
  testCounter++;
  loggerService.debug(`DEBUG test mesajı #${testCounter}`, {
    testType: 'debug',
    counter: testCounter,
    timestamp: new Date().toISOString(),
  });
}

/**
 * INFO seviyesi log testi
 */
function testInfoLog(): void {
  testCounter++;
  loggerService.info(`INFO test mesajı #${testCounter}`, {
    testType: 'info',
    counter: testCounter,
    userAction: 'button_click',
  });
}

/**
 * WARN seviyesi log testi
 */
function testWarnLog(): void {
  testCounter++;
  loggerService.warn(`WARN test mesajı #${testCounter}`, {
    testType: 'warning',
    counter: testCounter,
    warning: 'Bu bir test uyarısıdır',
  });
}

/**
 * ERROR seviyesi log testi
 */
function testErrorLog(): void {
  testCounter++;
  loggerService.error(`ERROR test mesajı #${testCounter}`, undefined, {
    testType: 'error',
    counter: testCounter,
    errorCode: 'TEST_ERROR',
  });
}

/**
 * FATAL seviyesi log testi
 */
function testFatalLog(): void {
  testCounter++;
  loggerService.fatal(`FATAL test mesajı #${testCounter}`, undefined, {
    testType: 'fatal',
    counter: testCounter,
    severity: 'critical',
  });
}

/**
 * Context ile log testi
 */
function testContextLog(): void {
  testCounter++;
  const complexContext = {
    user: {
      id: 123,
      name: 'Test User',
      roles: ['admin', 'user'],
    },
    request: {
      method: 'POST',
      url: '/api/test',
      headers: {
        'Content-Type': 'application/json',
      },
    },
    metadata: {
      testCounter,
      timestamp: new Date().toISOString(),
      browser: navigator.userAgent,
    },
  };

  loggerService.info('Karmaşık context ile test', complexContext);
}

/**
 * Error objesi ile log testi
 */
function testErrorObjectLog(): void {
  testCounter++;
  try {
    // Kasıtlı hata oluştur
    throw new Error(`Test hatası #${testCounter}`);
  } catch (error) {
    loggerService.error('Yakalanan test hatası', error as Error, {
      testType: 'error_object',
      counter: testCounter,
      errorSource: 'manual_test',
    });
  }
}

/**
 * Performans testi - çok sayıda log mesajı
 */
function testPerformance(): void {
  const startTime = performance.now();
  const logCount = 100;

  loggerService.info(`Performans testi başlatılıyor - ${logCount} log mesajı`);

  for (let i = 0; i < logCount; i++) {
    if (i % 4 === 0) {
      loggerService.debug(`Performans test DEBUG #${i}`);
    } else if (i % 4 === 1) {
      loggerService.info(`Performans test INFO #${i}`);
    } else if (i % 4 === 2) {
      loggerService.warn(`Performans test WARN #${i}`);
    } else {
      loggerService.error(`Performans test ERROR #${i}`);
    }
  }

  const endTime = performance.now();
  const duration = endTime - startTime;

  loggerService.info('Performans testi tamamlandı', {
    logCount,
    duration: `${duration.toFixed(2)}ms`,
    averagePerLog: `${(duration / logCount).toFixed(4)}ms`,
  });
}

/**
 * File log'ları göster
 * SOLID prensiplerinden Dependency Inversion Principle'a uygun olarak,
 * alt bileşen dosya log'larının nasıl alınacağına karar vermez,
 * bu kararı LoggerService interface'i verir.
 */
async function showFileLogs(): Promise<void> {
  try {
    // Service'den dosya log'larını al - service hangi storage'dan alacağına karar verir
    fileLogs.value = await loggerService.getFileLogs();

    loggerService.info("File log'ları gösterildi", {
      logCount: fileLogs.value.length,
    });
  } catch (error) {
    console.error("File log'ları gösterme hatası:", error);
    loggerService.error("File log'ları gösterme hatası", error as Error);
  }
}

/**
 * File log'ları temizle
 * SOLID prensiplerinden Dependency Inversion Principle'a uygun olarak,
 * alt bileşen dosya log'larının nasıl temizleneceğine karar vermez,
 * bu kararı LoggerService interface'i verir.
 */
async function clearFileLogs(): Promise<void> {
  try {
    // Service'den dosya log'larını temizle - service hangi storage'ı temizleyeceğine karar verir
    await loggerService.clearFileLogs();
    fileLogs.value = [];

    loggerService.info("File log'ları temizlendi");
  } catch (error) {
    console.error("File log'ları temizleme hatası:", error);
    loggerService.error("File log'ları temizleme hatası", error as Error);
  }
}
</script>

<style scoped>
.file-logs {
  max-height: 300px;
  overflow-y: auto;
  background-color: #f5f5f5;
  padding: 8px;
  border-radius: 4px;
  font-size: 12px;
  line-height: 1.4;
}

pre {
  white-space: pre-wrap;
  word-wrap: break-word;
}
</style>
