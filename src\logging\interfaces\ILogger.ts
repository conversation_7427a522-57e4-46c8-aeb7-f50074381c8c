// src/logging/interfaces/ILogger.ts

/**
 * @enum LogLevel
 * Loglama seviyelerini tanımlar.
 * Her seviye, kendisinden düşük seviyeleri de içerir.
 * Örneğin: INFO seviyesi DEBUG mesajlarını göstermez, ancak WARN ve ERROR mesajlarını gösterir.
 */
export enum LogLevel {
    DEBUG = 0,
    INFO = 1,
    WARN = 2,
    ERROR = 3,
    FATAL = 4,
}

/**
 * @interface LogEntry
 * Bir log girişinin yapısını tanımlar.
 * Tüm log mesajları bu yapıya uygun olarak oluşturulur.
 */
export interface LogEntry {
    level: LogLevel;
    message: string;
    timestamp: Date;
    context?: Record<string, unknown>; // İsteğe bağlı ek bilgiler
    error?: Error; // Hata durumunda error objesi
}

/**
 * @interface LoggerConfig
 * Logger yapılandırma seçeneklerini tanımlar.
 * .env dosyasından gelen değerler bu interface'e uygun olarak parse edilir.
 */
export interface LoggerConfig {
    level: LogLevel;
    toConsole: boolean;
    toFile: boolean;
    filePath?: string;
    maxFileSize?: number;
    dateFormat?: string;
}

/**
 * @interface ILoggerService
 * Loglama servisinin tüm operasyonlarını tanımlayan üst seviye interface.
 * SOLID prensiplerinden Dependency Inversion Principle'a uygun olarak,
 * alt bileşenler bu interface'e bağımlı olur, somut implementasyonlara değil.
 *
 * Bu interface, loglama ile ilgili tüm kararları kendi içinde alır:
 * - Hangi log seviyelerinin aktif olduğu
 * - Log'ların nereye yazılacağı
 * - Dosya log'larının nasıl yönetileceği
 * - Konfigürasyon bilgilerinin nasıl sunulacağı
 */
export interface ILoggerService extends ILogger {
    /**
     * Dosya log'larını getirir.
     * Hangi storage'dan alınacağına service karar verir.
     * @returns Log kayıtları dizisi
     */
    getFileLogs(): Promise<string[]>;

    /**
     * Dosya log'larını temizler.
     * Hangi storage'ın temizleneceğine service karar verir.
     */
    clearFileLogs(): Promise<void>;

    /**
     * Logger konfigürasyonunun UI için uygun formatını döndürür.
     * Alt bileşenler bu bilgiyi sadece görüntüleme amaçlı kullanır.
     * @returns UI için formatlanmış konfigürasyon bilgisi
     */
    getConfigurationForUI(): LoggerConfigurationUI;

    /**
     * Logger servisinin durumunu döndürür.
     * @returns Servis durum bilgisi
     */
    getServiceStatus(): LoggerServiceStatus;
}

/**
 * @interface LoggerConfigurationUI
 * Logger konfigürasyonunun UI'da gösterilmesi için gerekli bilgileri tanımlar.
 * Alt bileşenler bu interface'i kullanarak konfigürasyonu görüntüler,
 * ancak konfigürasyon kararları vermez.
 */
export interface LoggerConfigurationUI {
    level: {
        name: string;
        value: LogLevel;
    };
    outputs: {
        console: {
            enabled: boolean;
            status: 'active' | 'inactive';
        };
        file: {
            enabled: boolean;
            status: 'active' | 'inactive';
            path?: string;
            maxSize?: string;
        };
    };
    dateFormat: string;
    summary: {
        activeOutputs: string[];
        totalOutputs: number;
        status: 'full' | 'partial' | 'inactive';
        statusMessage: string;
        statusColor: 'positive' | 'warning' | 'negative';
    };
}

/**
 * @interface LoggerServiceStatus
 * Logger servisinin genel durumunu tanımlar.
 */
export interface LoggerServiceStatus {
    initialized: boolean;
    configurationSource: 'env' | 'custom' | 'default';
    lastActivity?: Date;
    totalLogsWritten: number;
    errors: string[];
}

/**
 * @interface ILogger
 * Logger servisi için temel arayüz.
 * SOLID prensiplerinden Interface Segregation Principle'a uygun olarak,
 * sadece loglama ile ilgili temel metotları tanımlar.
 *
 * Bu arayüz, somut logger uygulamalarının (ConsoleLogger, FileLogger vb.)
 * uyması gereken sözleşmedir.
 */
export interface ILogger {
    /**
     * Debug seviyesinde log mesajı yazar.
     * Geliştirme aşamasında detaylı bilgiler için kullanılır.
     * @param message Log mesajı
     * @param context İsteğe bağlı ek bilgiler
     */
    debug(message: string, context?: Record<string, unknown>): void;

    /**
     * Info seviyesinde log mesajı yazar.
     * Genel bilgilendirme mesajları için kullanılır.
     * @param message Log mesajı
     * @param context İsteğe bağlı ek bilgiler
     */
    info(message: string, context?: Record<string, unknown>): void;

    /**
     * Warning seviyesinde log mesajı yazar.
     * Uyarı mesajları için kullanılır.
     * @param message Log mesajı
     * @param context İsteğe bağlı ek bilgiler
     */
    warn(message: string, context?: Record<string, unknown>): void;

    /**
     * Error seviyesinde log mesajı yazar.
     * Hata mesajları için kullanılır.
     * @param message Log mesajı
     * @param error İsteğe bağlı error objesi
     * @param context İsteğe bağlı ek bilgiler
     */
    error(message: string, error?: Error, context?: Record<string, unknown>): void;

    /**
     * Fatal seviyesinde log mesajı yazar.
     * Kritik hatalar için kullanılır.
     * @param message Log mesajı
     * @param error İsteğe bağlı error objesi
     * @param context İsteğe bağlı ek bilgiler
     */
    fatal(message: string, error?: Error, context?: Record<string, unknown>): void;

    /**
     * Belirtilen seviyede log mesajı yazar.
     * Genel amaçlı loglama metodu.
     * @param level Log seviyesi
     * @param message Log mesajı
     * @param error İsteğe bağlı error objesi
     * @param context İsteğe bağlı ek bilgiler
     */
    log(level: LogLevel, message: string, error?: Error, context?: Record<string, unknown>): void;

    /**
     * Logger'ın mevcut yapılandırmasını döndürür.
     * @returns Logger yapılandırması
     */
    getConfig(): LoggerConfig;

    /**
     * Belirtilen log seviyesinin aktif olup olmadığını kontrol eder.
     * Performans optimizasyonu için kullanılır.
     * @param level Kontrol edilecek log seviyesi
     * @returns Seviye aktif ise true, değilse false
     */
    isLevelEnabled(level: LogLevel): boolean;
}
